import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import { RoleService } from 'src/role/services/role.service';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { Pricing } from 'src/organization/schemas/pricing.schema';

@Injectable()
export class MigrationItemType {
    constructor(
        @InjectModel(Purchase.name) private PurchaseModel: Model<PurchaseDocument>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
    ) { }

}
