import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";
import { PromotionTableName } from "src/promotions/repository/entities/promotion.entity";
import { ActiveTimeFrame, ActiveTimeFrameSchema } from "src/utils/schemas/active-time-frame.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DiscountType } from "src/utils/enums/discount.enum";
import { SessionType } from "src/utils/enums/session-type.enum";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
export type PricingDocument = HydratedDocument<Pricing>;

export class Discount {
    @Prop({ enum: DiscountType, required: false })
    type?: string;

    @Prop({ required: false, type: Number })
    value?: number;
}

export class Services {
    @Prop({ enum: ClassType, required: true })
    type: ClassType;

    @Prop({ type: SchemaTypes.ObjectId, ref: "ServiceCategory", required: true })
    serviceCategory: Types.ObjectId;

    @Prop({ type: [SchemaTypes.ObjectId], ref: "AppointmentType", default: [], required: false })
    appointmentType?: Types.ObjectId[];

    @Prop({ enum: SessionType, required: true })
    sessionType: SessionType;

    @Prop({ type: Number, required: true })
    sessionCount: number;

    @Prop({ type: Number, required: true })
    dayPassLimit: number;

    @Prop({ type: Number, required: false })
    sessionPerDay?: number;

    @Prop({ type: String, required: false })
    introductoryOffer?: string;

    @Prop({
        type: [
            {
                serviceCategory: { type: SchemaTypes.ObjectId, ref: "ServiceCategory", required: false },
                subTypeIds: { type: [SchemaTypes.ObjectId], ref: "AppointmentType", required: false },
            },
        ],
        required: false,
    })
    relationShip?: Array<{
        serviceCategory: Types.ObjectId;
        subTypeIds: Types.ObjectId[];
    }>;
}


@Schema({ timestamps: true })
export class Pricing {

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User", })
    createdBy?: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Organization", index: true })
    organizationId: string;

    /**
     * This is used to define the type of item
     * - This is used to define the type of item
     * - This could be used to filter the pricing
     */
    @Prop({ type: String, required: true, enum: ENUM_PRODUCT_ITEM_TYPE, index: true })
    itemType: ENUM_PRODUCT_ITEM_TYPE;

    @Prop({ type: String, required: true, index: true })
    name: string;

    @Prop({ type: Number, required: true })
    price: Number;

    @Prop({ type: Number, required: false })
    basePrice?: Number;

    @Prop({ type: Number, required: false })
    gstAmount?: Number;

    @Prop({ type: Number, required: false })
    finalPrice?: Number;

    @Prop({ type: Boolean, required: true })
    isSellOnline: Boolean;

    @Prop({ type: Boolean, default: false })
    isTrialPricing?: Boolean;

    @Prop({ type: Number, required: false, default: 0 })
    tax?: Number;

    @Prop({ type: Services, required: false })
    services?: Services;

    @Prop({ type: Number, required: true })
    expiredInDays: number;

    @Prop({ type: String, required: false })
    hsnOrSacCode?: string;

    @Prop({ type: String, required: false })
    durationUnit?: string;

    @Prop({ type: Boolean, required: false, default: false })
    isInclusiveofGst?: Boolean;

    @Prop({ type: SchemaTypes.ObjectId, required: false })
    membershipId?: string;

    /**
     *@deprecated
     * This is not used anywhere. Use promotion instead
     * This is used to store the discount details for the pricing
     */
    @Prop({ type: Discount, required: false })
    discount?: Discount;

    /**
     *
     * This is used to store the default promotion id.
     * - This will auto apply the promotion to the pricing
     */
    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: PromotionTableName, default: null })
    promotion?: Types.ObjectId;

    @Prop({ type: Boolean, default: true, index: true })
    isActive: Boolean;

    @Prop({ type: [SchemaTypes.ObjectId], ref: "Pricing", required: false })
    pricingIds?: Types.ObjectId[];

    @Prop({ type: Boolean, default: false })
    isBundledPricing?: Boolean;

    @Prop({ type: SchemaTypes.ObjectId, required: false, default: null, ref: "Organization.revenueCategory" })
    revenueCategory?: string;

    /**
     * Active time frames define when this pricing package is available for purchase in POS
     * If empty, the pricing is always available
     */
    @Prop({ type: [ActiveTimeFrameSchema], required: false, default: [] })
    activeTimeFrames?: ActiveTimeFrame[];

    @Prop({ type: String, required: false, default: "" })
    description?: string;

    @Prop({ type: String, required: false })
    image?: string;

    @Prop({ type: Boolean, required: false })
    isFeatured?: boolean;
}

export const PricingSchema = SchemaFactory.createForClass(Pricing);

// Prick