import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { ENUM_PROMOTION_TARGET } from 'src/promotions/enums/promotion-target.enum';
import { DiscountDto } from 'src/organization/dto/create-pricing.dto';

export class PromotionDto {
  @ApiProperty({
    description: 'The ID of the promotion',
    example: '60d21b4667d0d8992e610c85',
  })
  _id: string;

  @ApiProperty({
    description: 'The name of the promotion',
    example: 'Summer Sale',
  })
  name: string;

  @ApiProperty({
    description: 'The coupon code for the promotion',
    example: 'SUMMER2023',
  })
  couponCode: string;

  @ApiProperty({
    description: 'The description of the promotion',
    example: 'Summer sale discount',
  })
  description: string;

  @ApiProperty({
    description: 'The type of promotion',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
  })
  type: string;

  @ApiProperty({
    description: 'The value of the promotion',
    example: 10,
  })
  value: number;

  @ApiProperty({
    description: 'The target of the promotion',
    enum: ENUM_PROMOTION_TARGET,
    example: ENUM_PROMOTION_TARGET.ALL_USERS,
  })
  target: string;

  @ApiProperty({
    description: 'The item type the promotion applies to',
    enum: ENUM_PRODUCT_ITEM_TYPE,
    example: ENUM_PRODUCT_ITEM_TYPE.PRODUCT,
  })
  itemType: string;

  @ApiProperty({
    description: 'Whether the promotion is active',
    example: true,
  })
  isActive: boolean;
  @ApiProperty({
    description: 'The name of the promotion',
    example: 'Student Id',
  })
  promotionLabel?: string;

}

export class CartItemDetailDto {
  @ApiProperty({
    description: 'The ID of the item',
    example: '60d21b4667d0d8992e610c85',
  })
  _id: string;

  @ApiProperty({
    description: 'The organization ID',
    example: '60d21b4667d0d8992e610c85',
  })
  organizationId: string;

  @ApiProperty({
    description: 'The name of the item',
    example: 'Premium Yoga Mat',
  })
  name: string;

  @ApiProperty({
    description: 'The price of the item',
    example: 999,
  })
  price: number;

  @ApiProperty({
    description: 'Whether the item is sold online',
    example: true,
  })
  isSellOnline: boolean;

  @ApiProperty({
    description: 'The tax percentage',
    example: 12,
  })
  tax: number;

  @ApiProperty({
    description: 'The number of days until expiry',
    example: 2,
    required: false,
  })
  expiredInDays?: number;

  @ApiProperty({
    description: 'HSN or SAC code for tax purposes',
    example: '*********',
    required: false,
  })
  hsnOrSacCode?: string;

  @ApiProperty({
    description: 'The duration unit',
    example: 'days',
    required: false,
  })
  durationUnit?: string;

  @ApiProperty({
    description: 'The promotion applied to this item',
    type: PromotionDto,
    required: false,
  })
  promotion?: PromotionDto;

  @ApiProperty({
    description: 'Additional manual discount applied to this item',
    required: false,
  })
  additionalDiscount?: DiscountDto; //{ type: DiscountType; value: number };

  @ApiProperty({
    description: "Discounted by person",
    example: "None"
  })
  discountedBy?: string

  @ApiProperty({
    description: 'Whether the item is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'The pricing IDs',
    type: [String],
    example: [],
  })
  pricingIds: string[];

  @ApiProperty({
    description: 'Whether the item has bundled pricing',
    example: false,
  })
  isBundledPricing: boolean;

  @ApiProperty({
    description: 'The type of item',
    enum: ENUM_PRODUCT_ITEM_TYPE,
    example: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
  })
  itemType: ENUM_PRODUCT_ITEM_TYPE;

  @ApiProperty({
    description: 'The quantity of the item',
    example: 32,
  })
  quantity: number;

  @ApiProperty({
    description: 'The total price for this item (quantity * price)',
    example: 3200,
  })
  totalPrice: number;

  @ApiProperty({
    description: 'The item-level discount amount applied to this item',
    example: 1440,
  })
  discountAmount: number;

  @ApiProperty({
    description: 'The item-level discount type applied to this item',
    example: 'flat',
  })
  discountType: string;

  @ApiProperty({
    description: 'The item-level discount value applied to this item',
    example: 45,
  })
  discountValue: number;

  @ApiProperty({
    description: 'The cart-level promotion discount amount applied to this item',
    example: 200,
    required: false,
  })
  cartDiscountAmount?: number;

  @ApiProperty({
    description: 'The cart-level promotion discount type applied to this item',
    example: 'percentage',
    required: false,
  })
  cartDiscountType?: string;

  @ApiProperty({
    description: 'The cart-level promotion discount value applied to this item',
    example: 10,
    required: false,
  })
  cartDiscountValue?: number;

  @ApiProperty({
    description: 'The manual discount amount applied to this item',
    example: 100,
    required: false,
  })
  manualDiscountAmount?: number;

  @ApiProperty({
    description: 'The manual discount type applied to this item',
    example: 'flat',
    required: false,
  })
  manualDiscountType?: string;

  @ApiProperty({
    description: 'The manual discount value applied to this item',
    example: 100,
    required: false,
  })
  manualDiscountValue?: number;

  @ApiProperty({
    description: 'The tax rate for this item',
    example: 18,
  })
  taxRate: number;

  @ApiProperty({
    description: 'The tax amount for this item',
    example: 316.8,
  })
  taxAmount: number;

  @ApiProperty({
    description: 'The price after item-level discount',
    example: 1760,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'The final price after discounts and taxes',
    example: 2076.8,
  })
  finalPrice: number;

  @ApiProperty({
    description: 'Whether this item is valid',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'The variant ID for products (if applicable)',
    example: '60d21b4667d0d8992e610c86',
    required: false,
  })
  variantId?: string;

  @ApiProperty({
    description: 'Any validation errors for this item',
    example: [],
    required: false,
  })
  validationErrors?: string[];

  @ApiProperty({
    description: 'The return discount amount applied to this item',
    example: 100,
    required: false,
  })
  returnDiscountAmount?: number;

  @ApiProperty({
    description: 'The voucher discount amount applied to this item',
    example: 50,
    required: false,
  })
  voucherDiscountAmount?: number;

  @ApiProperty({
    description: 'Promotion level',
    example: 'student-123',
    required: false,
  })
  promotionLabel?: string;

  @ApiProperty({
    description: 'promotion student Id',
    example: 'student_Id',
    required: false,
  })
  promotionLabelKey?: string;

  // @ApiProperty({
  //   description: 'The purchase ID for this item',
  //   example: '60d21b4667d0d8992e610c85',
  //   required: false,
  // })
  @ApiHideProperty()
  purchaseId?: string;
}

export class CartResponseDto {
  @ApiProperty({
    description: 'The items in the cart with detailed information',
    type: [CartItemDetailDto],
  })
  items: CartItemDetailDto[];

  @ApiProperty({
    description: 'The promotion applied to the cart',
    type: PromotionDto,
    required: false,
  })
  promotion?: PromotionDto;

  @ApiProperty({
    description: 'The type of cart-level promotion discount',
    example: 'Percentage',
    required: false,
  })
  cartDiscountType?: DiscountType | "";

  @ApiProperty({
    description: 'The value of the cart-level promotion discount',
    example: 10,
    required: false,
  })
  cartDiscountValue?: number;

  @ApiProperty({
    description: 'The subtotal of all items before discounts and taxes (alternative name)',
    example: 3200,
  })
  subTotal: number;

  @ApiProperty({
    description: 'The subtotal of all items before discounts and taxes (alternative name)',
    example: 3200,
  })
  subTotalAfterItemLevelDiscount: number;

  @ApiProperty({
    description: 'The item-level discount amount applied',
    example: 1000,
  })
  itemDiscount: number;

  @ApiProperty({
    description: 'The cart-level promotion discount amount applied',
    example: 240,
  })
  cartDiscount: number;

  @ApiProperty({
    description: 'The manual discount amount applied',
    example: 200,
  })
  cartDiscountAmount: number;

  @ApiProperty({
    description: 'The total discount amount applied (item + cart)',
    example: 1440,
  })
  totalDiscount: number;

  @ApiProperty({
    description: 'The total discount amount applied (alternative name for backward compatibility)',
    example: 1440,
  })
  discount: number;

  @ApiProperty({
    description: 'The user who applied the discount',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  discountedBy?: string

  @ApiProperty({
    description: 'The total tax amount',
    example: 316.8,
  })
  totalTax: number;

  @ApiProperty({
    description: 'The total tax amount (alternative name)',
    example: 316.8,
  })
  totalGstValue: number;

  @ApiProperty({
    description: 'The total amount after GST',
    example: 2076.8,
  })
  totalAmountAfterGst: number;

  @ApiProperty({
    description: 'The round off amount',
    example: 0.8,
  })
  roundOff: number;

  @ApiProperty({
    description: 'The grand total after discounts and taxes',
    example: 2076,
  })
  grandTotal: number;

  @ApiProperty({
    description: 'The amount paid',
    example: 2076.8,
  })
  amountPaid: number;

  @ApiProperty({
    description: 'Any validation errors for the cart',
    example: [],
    required: false,
  })
  validationErrors?: string[];

  @ApiProperty({
    description: 'Return discount amount from returned purchases',
    example: 500,
    required: false,
  })
  returnDiscount?: number;

  @ApiProperty({
    description: 'Items returned in the cart',
    type: [CartItemDetailDto],
    required: false,
  })
  returnItems?: CartItemDetailDto[];

  @ApiProperty({
    description: 'Details of returned purchases',
    required: false,
  })
  returnDetails?: {
    returnPurchaseIds: string[];
    returnTotal: number;
  };

  @ApiProperty({
    description: 'Details of returned custom package purchases',
    required: false,
  })
  returnCustomPackageDetails?: {
    returnPurchaseIds: string[];
    returnTotal: number;
  };

  @ApiProperty({
    description: 'Voucher discount amount applied to the cart',
    example: 200,
    required: false,
  })
  voucherDiscount?: number;

  @ApiProperty({
    description: 'Voucher items used in the cart',
    type: [CartItemDetailDto],
    required: false,
  })
  voucherItems?: CartItemDetailDto[];

  @ApiProperty({
    description: 'Details of vouchers applied to the cart',
    required: false,
  })
  voucherDetails?: {
    voucherIds: string[];
    totalVoucherAmount: number;
    usedVoucherAmount: number;
    remainingVoucherAmount: number;
    vouchers: {
      _id: string;
      name: string;
      amount: number;
    }[];
  };
}
