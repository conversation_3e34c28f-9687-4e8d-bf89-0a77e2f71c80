
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { Model, PipelineStage, Types } from 'mongoose';
import { IDatabaseCreateOptions, IDatabaseFindAllOptions, IDatabaseFindOneOptions, IDatabaseOptions, IDatabaseSaveOptions } from 'src/common/database/interfaces/database.interface';
import { CreatePromotionDto } from '../dto/create-promotion.dto';
import { UpdatePromotionDto } from '../dto/update-promotion.dto';
import { ApplyPromotionDto, ApplyPromotionToItemsDto } from '../dto/apply-promotion.dto';
import { PromotionRepository } from '../repository/repositories/promotion.repository';
import { PromotionItemRepository } from '../repository/repositories/promotion-item.repository';
import { PromotionDocument, PromotionEntity } from '../repository/entities/promotion.entity';
import { PromotionItemEntity, PromotionItemTableName } from '../repository/entities/promotion-item.entity';
import { ENUM_PRODUCT_ITEM_TYPE } from '../enums/item-type.enum';
import { Pricing } from 'src/organization/schemas/pricing.schema';
import { InjectModel } from '@nestjs/mongoose';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { ENUM_PROMOTION_TARGET } from '../enums/promotion-target.enum';
import { ProductVariant } from 'src/merchandise/schema/product-variant.schema';
import { Inventory } from 'src/merchandise/schema/inventory.schema';
import { Product } from 'src/merchandise/schema/product.schema';


@Injectable()
export class PromotionService {
    constructor(
        private readonly promotionRepository: PromotionRepository,
        private readonly promotionItemRepository: PromotionItemRepository,
        @InjectModel(Pricing.name) private readonly PricingModel: Model<Pricing>,
        @InjectModel(Product.name) private readonly ProductModel: Model<Product>,
        @InjectModel(ProductVariant.name) private readonly ProductVariantModel: Model<ProductVariant>,
        @InjectModel(Inventory.name) private readonly InventoryModel: Model<Inventory>,

    ) { }

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<PromotionDocument[]> {
        return this.promotionRepository.findAll(find, options);
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionDocument> {
        return this.promotionRepository.findOneById(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionDocument> {
        return this.promotionRepository.findOne(find, options);
    }

    async create(
        organizationId: Types.ObjectId,
        createPromotionDto: CreatePromotionDto,
        options?: IDatabaseCreateOptions
    ): Promise<PromotionDocument> {
        const promotion = new PromotionEntity();
        promotion.organizationId = organizationId;
        promotion.name = createPromotionDto.name;
        promotion.description = createPromotionDto.description;
        promotion.type = createPromotionDto.type;
        promotion.value = createPromotionDto.value;
        promotion.target = createPromotionDto.target;
        promotion.isActive = true;
        promotion.itemType = createPromotionDto.itemType;
        promotion.startDate = createPromotionDto.startDate;
        promotion.endDate = createPromotionDto.endDate;
        promotion.promotionLabel = createPromotionDto?.promotionLabel
        // promotion.timeWindow = createPromotionDto.timeWindow;

        // Add facility IDs if provided
        if (createPromotionDto.facilityIds && createPromotionDto.facilityIds.length > 0) {
            promotion.facilityIds = createPromotionDto.facilityIds.map(id => new Types.ObjectId(id));
        }

        return this.promotionRepository.create<PromotionEntity>(promotion, options);
    }

    async update(
        promotionId: Types.ObjectId,
        organizationId: Types.ObjectId,
        updatePromotionDto: UpdatePromotionDto,
        options?: IDatabaseSaveOptions
    ): Promise<PromotionDocument> {
        const promotion = await this.promotionRepository.findOneById(promotionId);
        if (!promotion) {
            throw new NotFoundException('Promotion not found');
        }

        if (!promotion.organizationId.equals(organizationId)) {
            throw new BadRequestException('You do not have permission to update this promotion');
        }

        if (updatePromotionDto.name) {
            promotion.name = updatePromotionDto.name;
        }

        if (updatePromotionDto.description !== undefined) {
            promotion.description = updatePromotionDto.description;
        }

        if (updatePromotionDto.type) {
            promotion.type = updatePromotionDto.type;
        }

        if (updatePromotionDto.value !== undefined) {
            promotion.value = updatePromotionDto.value;
        }

        if (updatePromotionDto.target) {
            promotion.target = updatePromotionDto.target;
        }

        if (updatePromotionDto.isActive) {
            promotion.isActive = updatePromotionDto.isActive;
        }

        if (updatePromotionDto.startDate) {
            promotion.startDate = updatePromotionDto.startDate;
        }

        if (updatePromotionDto.endDate) {
            promotion.endDate = updatePromotionDto.endDate;
        }
        if (updatePromotionDto.promotionLabel) {
            promotion.promotionLabel = updatePromotionDto.promotionLabel
        }
        // Update facility IDs if provided
        if (updatePromotionDto.facilityIds) {
            promotion.facilityIds = updatePromotionDto.facilityIds.map(id => new Types.ObjectId(id));
        } else {
            promotion.facilityIds = [];
        }

        // if (updatePromotionDto.timeWindow) {
        //     promotion.timeWindow = updatePromotionDto.timeWindow;
        // }

        return this.promotionRepository.save(promotion, options);
    }

    async delete(
        promotionId: Types.ObjectId,
        organizationId: Types.ObjectId
    ): Promise<boolean> {
        const promotion = await this.promotionRepository.findOneById(promotionId);

        if (!promotion) {
            throw new NotFoundException('Promotion not found');
        }

        if (!promotion.organizationId.equals(organizationId)) {
            throw new BadRequestException('You do not have permission to delete this promotion');
        }

        // Soft delete the promotion
        await this.promotionRepository.softDelete(promotion);

        // Delete all promotion items associated with this promotion
        await this.promotionItemRepository.softDeleteMany({
            promotionId: promotionId
        });

        return true;
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<number> {
        return this.promotionRepository.getTotal(find, options);
    }

    async getPromotions(
        find: Record<string, any>,
        options?: IDatabaseFindAllOptions,
        pricingFilter?: {
            pricingId: Types.ObjectId;
            includePricing: boolean;
        }
    ): Promise<{ data: PromotionDocument[], total: number }> {
        const { offset, limit } = options.paging;

        // If pricing filter is provided, use aggregation pipeline
        if (pricingFilter && pricingFilter.pricingId) {
            // Build the aggregation pipeline
            const pipeline: PipelineStage[] = [
                // Initial match stage with basic filters
                { $match: find },

                // Lookup promotion items to check if this promotion is mapped to the specified pricing ID
                {
                    $lookup: {
                        from: 'promotionItems',
                        let: { promotionId: '$_id' },
                        pipeline: [
                            {
                                $match: {
                                    deletedAt: {
                                        $exists: false
                                    },
                                    $expr: {
                                        $and: [
                                            { $eq: ['$promotion', '$$promotionId'] },
                                            { $eq: ['$item', pricingFilter.pricingId] },
                                            { $eq: ['$isActive', true] }
                                        ]
                                    }
                                }
                            }
                        ],
                        as: 'pricingItems'
                    }
                },

                // Filter based on whether we want to include or exclude promotions with this pricing
                // {
                //     $match: pricingFilter.includePricing
                //         ? { pricingItems: { $ne: [] } }  // Include promotions with this pricing
                //         : { pricingItems: { $eq: [] } }  // Exclude promotions with this pricing
                // },
                {
                    $addFields: {
                        selected: {
                            $cond: {
                                if: { $eq: [{ $size: "$pricingItems" }, 0] },
                                then: false,
                                else: true
                            }
                        }
                    }
                },

                // Lookup facility names for display
                {
                    $lookup: {
                        from: 'facilities',
                        localField: 'facilityIds',
                        foreignField: '_id',
                        as: 'facilityIds',
                        pipeline: [
                            {
                                $project: {
                                    facilityName: 1
                                }
                            }
                        ]
                    }
                },

                // Add facet for pagination and total count
                {
                    $facet: {
                        data: [
                            {
                                $sort: {
                                    selected: -1,
                                    ...(options?.order?.field
                                        ? { [options.order.field]: options.order.value === 'asc' ? 1 : -1 }
                                        : { createdAt: -1 })
                                }
                            },
                            { $skip: offset },
                            { $limit: limit },
                            // Remove the lookup fields we don't need in the final result
                            {
                                $unset: ["pricingItems"]
                            }
                        ],
                        total: [{ $count: 'count' }]
                    }
                }
            ];

            // Execute the aggregation pipeline
            const result = await this.promotionRepository.aggregate(pipeline);

            // Format the result to match the expected return type
            const data = result[0]?.data || [];
            const total = result[0]?.total[0]?.count || 0;

            return {
                data: data as PromotionDocument[],
                total
            };
        }

        // If no pricing filters, use the standard approach
        const [data, total] = await Promise.all([
            this.promotionRepository.findAll(find, {
                paging: {
                    limit: limit,
                    offset: offset
                },
                order: options?.order,
                join: [
                    {
                        path: 'facilityIds',
                        select: 'facilityName',
                    }
                ]
            }),
            this.promotionRepository.getTotal(find)
        ]);

        return { data, total };
    }

    async createNewPromotion(
        organizationId: Types.ObjectId,
        createPromotionDto: CreatePromotionDto
    ): Promise<PromotionDocument> {
        // Convert facilityIds strings to ObjectIds if provided
        const session = await this.promotionRepository.startTransaction();
        session.startTransaction();
        try {
            let itemDocs = [];
            const promotion = await this.create(organizationId, createPromotionDto, { session });
            if (createPromotionDto.items?.length && createPromotionDto.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE) {
                itemDocs = await this.PricingModel.find({ _id: { $in: createPromotionDto.items?.map(item => new Types.ObjectId(item.itemId)) } }, { _id: 1, name: 1, organizationId: 1, price: 1 });
            } else if (createPromotionDto.items?.length && createPromotionDto.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
                itemDocs = await this.ProductModel.find({ _id: { $in: createPromotionDto.items?.map(item => new Types.ObjectId(item.itemId)) } }, { _id: 1, name: 1, organizationId: 1 });
            }
            const promotionItems = await Promise.all(createPromotionDto.items?.map(async item => {
                const itemDoc = itemDocs.find(doc => doc._id.equals(item.itemId));
                if (!itemDoc) {
                    throw new NotFoundException(`One of the selected service/product items not found`);
                }
                if (itemDoc.organizationId.toString() !== organizationId.toString()) {
                    throw new BadRequestException(`You do not have permission to apply this promotion on ${itemDoc.name} service`);
                }
                if (item.itemType !== ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
                    await this.validateItemDiscount(promotion, itemDoc.price || 0);
                }
                const promotionItem = new PromotionItemEntity();
                promotionItem.organizationId = organizationId;
                promotionItem.promotion = promotion._id;
                promotionItem.itemType = item.itemType;
                promotionItem.item = new Types.ObjectId(item.itemId);
                promotionItem.variantId = item.variantId ? new Types.ObjectId(item.variantId) : undefined;
                promotionItem.isActive = true;
                return promotionItem;
            }));

            if (promotionItems && promotionItems?.length > 0) {
                await this.promotionItemRepository.createMany(promotionItems, { session });
            }
            await session.commitTransaction();
            return promotion;
        } catch (error) {
            console.error('Error creating promotion:', error);
            await session.abortTransaction();
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updatePromotion(
        promotionId: Types.ObjectId,
        organizationId: Types.ObjectId,
        updatePromotionDto: UpdatePromotionDto,
    ): Promise<PromotionDocument> {
        const session = await this.promotionRepository.startTransaction();
        session.startTransaction();
        try {
            // 1. Update promotion basic details
            const promotion = await this.update(
                promotionId,
                organizationId,
                updatePromotionDto,
                { session }
            );

            if (!promotion) {
                throw new NotFoundException('Promotion not found');
            }

            // 2. Process items batch by batch
            const itemsToAdd: any[] = [];
            const itemIds = updatePromotionDto.items.map(item =>
                new Types.ObjectId(item.itemId)
            );

            // 2.1 Find existing mappings for the items in the update request
            const existingMappings = await this.promotionItemRepository.findAll({
                promotion: promotionId,
                organizationId: organizationId,
                item: { $in: itemIds }
            }, { session });

            const existingItemMap = new Map(
                existingMappings.map(item => [item.item.toString(), item])
            );

            // 2.2 Process items from the DTO
            for (const item of updatePromotionDto.items) {
                const itemIdStr = item.itemId.toString();
                const existingItem = existingItemMap.get(itemIdStr);

                if (!existingItem) {
                    // New item to add
                    let itemDoc = null;
                    if (item.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE) {
                        itemDoc = await this.PricingModel.findOne({ _id: new Types.ObjectId(item.itemId) }, { _id: 1, name: 1, organizationId: 1, price: 1 });
                    } else if (item.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
                        itemDoc = await this.ProductModel.findOne({ _id: new Types.ObjectId(item.itemId) }, { _id: 1, name: 1, organizationId: 1, price: 1 });
                    }

                    if (!itemDoc) {
                        throw new NotFoundException(`One of the items not found`);
                    }
                    if (itemDoc.organizationId.toString() !== organizationId.toString()) {
                        throw new BadRequestException(`You do not have permission to apply this promotion on ${itemDoc.name} service`);
                    }
                    this.validateItemDiscount(promotion, itemDoc.price);
                    itemsToAdd.push({
                        organizationId: organizationId,
                        promotion: promotionId,
                        item: new Types.ObjectId(item.itemId),
                        itemType: promotion.itemType,
                        isActive: true,
                    });
                }
            }

            // 2.3 Remove any mappings for items not in the update request
            await this.promotionItemRepository.deleteMany({
                promotion: promotionId,
                organizationId: organizationId,
                item: { $nin: itemIds }
            }, { session });

            // 2.4 Add new mappings in batches
            if (itemsToAdd.length > 0) {
                const BATCH_SIZE = 100;
                for (let i = 0; i < itemsToAdd.length; i += BATCH_SIZE) {
                    const batch = itemsToAdd.slice(i, i + BATCH_SIZE);
                    await this.promotionItemRepository.createMany(batch, { session });
                }
            }

            await session.commitTransaction();

            // 3. Return updated promotion
            return this.promotionRepository.findOneById(
                promotionId,
            );
        } catch (error) {
            await session.abortTransaction();
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updatePromotionStatus(
        promotionId: Types.ObjectId,
        organizationId: Types.ObjectId,
        status: boolean,
    ): Promise<PromotionDocument> {
        const promotion = await this.promotionRepository.findOneById(promotionId);

        if (!promotion) {
            throw new NotFoundException('Promotion not found');
        }

        if (!promotion.organizationId.equals(organizationId)) {
            throw new BadRequestException('You do not have permission to update this promotion');
        }

        promotion.isActive = !!status;
        await promotion.save();

        return promotion;
    }

    async applyPromotion(
        organizationId: Types.ObjectId,
        promotionId: Types.ObjectId,
        { itemType, itemIds }: ApplyPromotionDto
    ): Promise<boolean> {
        const promotion = await this.promotionRepository.findOneById(new Types.ObjectId(promotionId));

        if (!promotion) {
            throw new NotFoundException('Promotion not found');
        }

        if (!promotion.organizationId.equals(organizationId)) {
            throw new BadRequestException('You do not have permission to apply this promotion');
        }

        // If itemIds are provided, apply to specific items
        if (itemIds && itemIds.length > 0) {
            // First, remove any existing promotion items for this promotion and these items
            await this.promotionItemRepository.deleteMany({
                promotionId: new Types.ObjectId(promotionId),
                itemId: { $in: itemIds.map(id => new Types.ObjectId(id)) }
            });

            // Create new promotion items for each item
            const promotionItems = itemIds.map(itemId => {
                const promotionItem = new PromotionItemEntity();
                promotionItem.organizationId = organizationId;
                promotionItem.promotion = new Types.ObjectId(promotionId);
                promotionItem.itemType = itemType;
                promotionItem.item = new Types.ObjectId(itemId);
                promotionItem.isActive = true;
                return promotionItem;
            });

            await this.promotionItemRepository.createMany(promotionItems);
        }

        return true;
    }

    async applyPromotionsToItem(
        organizationId: Types.ObjectId | string,
        { applyPromotions: apply, removePromotions: remove }: ApplyPromotionToItemsDto,
        itemDetails: {
            itemType: ENUM_PRODUCT_ITEM_TYPE;
            _id: Types.ObjectId;
            price: number;
        },
        options?: IDatabaseOptions
    ): Promise<boolean> {
        let isLocalSession = false;
        let { session } = options || {};
        if (!session) {
            session = await this.promotionRepository.startTransaction();
            session.startTransaction();
            isLocalSession = true;
        }
        const applyPromotionsIds = apply?.map(id => new Types.ObjectId(id)) || [];
        const removePromotionsIds = remove?.map(id => new Types.ObjectId(id)) || [];

        try {
            // check & validate apply promotions
            if (apply && apply.length > 0) {
                const promotions = await this.promotionRepository.findAll({
                    _id: { $in: applyPromotionsIds },
                    organizationId
                }, { select: { _id: 1, isActive: 1, endDate: 1 }, session, ...options });

                if (promotions.length !== apply.length) {
                    throw new NotFoundException('One or more promotions not found');
                }

                // validate promotions value and item value
                for (const promotion of promotions) {
                    await this.validateItemDiscount(promotion, itemDetails.price);
                }
            }

            if (apply && apply.length > 0) {
                // Check if already item is applied to promotion
                const existingPromotions = await this.promotionItemRepository.findAll({
                    organizationId,
                    item: itemDetails._id,
                    promotion: { $in: applyPromotionsIds }
                }, { select: { promotion: 1 }, session, ...options });

                const promotionItems = apply.filter(id => !existingPromotions.find(promotion => promotion.promotion.equals(id))).map(promotionId => {
                    const promotionItem = new PromotionItemEntity();
                    promotionItem.organizationId = (organizationId instanceof Types.ObjectId ? organizationId : new Types.ObjectId(organizationId));
                    promotionItem.promotion = new Types.ObjectId(promotionId);
                    promotionItem.itemType = itemDetails?.itemType || ENUM_PRODUCT_ITEM_TYPE.SERVICE;
                    promotionItem.item = itemDetails._id;
                    promotionItem.isActive = true;
                    return promotionItem;
                });
                await this.promotionItemRepository.createMany(promotionItems, { session, ...options });
            }

            if (remove && remove.length > 0) {
                await this.promotionItemRepository.deleteMany({
                    organizationId,
                    promotion: { $in: removePromotionsIds },
                    item: itemDetails._id
                }, { session, ...options });
            }
            if (isLocalSession) {
                await session.commitTransaction();
            }
            return true;
        } catch (error) {
            if (isLocalSession) {
                await session.abortTransaction();
            }
            throw error;
        } finally {
            if (isLocalSession) {
                session.endSession();
            }
        }
    }

    async removePromotion(
        organizationId: Types.ObjectId,
        promotionId: string,
        itemId?: string,
        categoryId?: string
    ): Promise<boolean> {
        const find: Record<string, any> = {
            organizationId,
            promotionId: new Types.ObjectId(promotionId)
        };

        if (itemId) {
            find.itemId = new Types.ObjectId(itemId);
        }

        if (categoryId) {
            find.categoryId = new Types.ObjectId(categoryId);
        }

        await this.promotionItemRepository.deleteMany(find);
        return true;
    }

    async getActivePromotions(
        organizationId: Types.ObjectId,
        itemType: ENUM_PRODUCT_ITEM_TYPE,
        itemId?: string,
        categoryId?: string
    ): Promise<PromotionDocument[]> {
        // Find all active promotions for the given item or category
        const find: Record<string, any> = {
            organizationId,
            itemType,
            isActive: true
        };

        if (itemId) {
            find.itemId = new Types.ObjectId(itemId);
        }

        if (categoryId) {
            find.categoryId = new Types.ObjectId(categoryId);
        }

        const promotionItems = await this.promotionItemRepository.findAll(find);

        if (promotionItems.length === 0) {
            return [];
        }

        const promotionIds = promotionItems.map(item => item.promotion);

        // Get the actual promotion details
        const now = new Date();
        const promotions = await this.promotionRepository.findAll({
            _id: { $in: promotionIds },
            status: true,
            $or: [
                { startDate: { $exists: false } },
                { startDate: { $lte: now } }
            ],
            $and: [
                {
                    $or: [
                        { endDate: { $exists: false } },
                        { endDate: { $gte: now } }
                    ]
                }
            ]
        });

        // Filter by time window if present
        const currentTime = new Date().toTimeString().substring(0, 5); // Format: HH:MM
        return promotions.filter(promotion => {
            if (!promotion.timeWindow) {
                return true;
            }

            return promotion.timeWindow.startTime <= currentTime &&
                promotion.timeWindow.endTime >= currentTime;
        });
    }

    async getPromotionPricingSetting(
        find: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<any> {
        const { serviceId, promotionId, organizationId, search, facilityId } = find;
        const { offset, limit } = options?.paging || { offset: 0, limit: 10 };

        // Convert facilityId to array of ObjectIds if provided
        const facilityIds = facilityId ?
            (Array.isArray(facilityId) ? facilityId.map(id => new Types.ObjectId(id)) : [new Types.ObjectId(facilityId)])
            : [];

        let match: any = {
            organizationId: new Types.ObjectId(organizationId),
            isActive: true,
        };
        if (find.classType) {
            match["services.type"] = find.classType
        }
        if (serviceId) {
            match['$or'] = [
                {
                    "services.serviceCategory": new Types.ObjectId(serviceId),
                },
                {
                    "services.relationShip.serviceCategory": new Types.ObjectId(serviceId),
                }
            ]
        }

        if (search?.length > 0) {
            match = {
                ...match,
                ...search
            }
        }

        const pipeline: PipelineStage[] = [
            // Match pricing with the given service ID
            {
                $match: match
            },
            // Lookup promotion items to check if pricing is mapped
            ...promotionId ?
                [{
                    $lookup: {
                        from: PromotionItemTableName,
                        let: { pricingId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$item", "$$pricingId"] },
                                            { $eq: ["$promotion", new Types.ObjectId(promotionId)] },
                                            { $eq: ["$isActive", true] },
                                            ...(facilityIds.length ? [
                                                {
                                                    $or: [
                                                        // Check if any of the provided facilityIds exist in the facilityIds array
                                                        { $gt: [{ $size: { $setIntersection: ["$facilityIds", facilityIds] } }, 0] },
                                                        // Or if facilityIds is empty/null (meaning applies to all facilities)
                                                        {
                                                            $or: [
                                                                { $eq: [{ $size: "$facilityIds" }, 0] },
                                                                { $eq: ["$facilityIds", null] }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ] : [])
                                        ]
                                    }
                                }
                            }
                        ],
                        as: "promotionMapping"
                    }
                }] : [
                    {
                        $addFields: {
                            promotionMapping: []
                        }
                    }
                ],
            // Lookup service details
            {
                $lookup: {
                    from: "services",
                    localField: "services.serviceCategory",
                    foreignField: "_id",
                    as: "serviceDetails"
                }
            },
            // Add computed fields
            {
                $addFields: {
                    selected: {
                        $cond: {
                            if: { $gt: [{ $size: "$promotionMapping" }, 0] },
                            then: true,
                            else: false
                        }
                    },
                    serviceName: { $arrayElemAt: ["$serviceDetails.name", 0] }
                }
            },
            // Project only needed fields
            {
                $project: {
                    _id: 1,
                    name: 1,
                    price: 1,
                    isActive: 1,
                    selected: 1,
                }
            },
            // Sort by creation date
            // {
            //     $sort: { createdAt: -1 }
            // },
            // Pagination
            {
                $facet: {
                    total: [{ $count: "total" }],
                    data: [
                        { $sort: { selected: -1, name: 1 } },
                        { $skip: offset },
                        { $limit: limit }
                    ]
                }
            },
        ];
        const result = await this.PricingModel.aggregate(pipeline);

        return {
            data: result[0]?.data || [],
            total: result[0]?.total[0]?.total || 0
        };
    }

    async getPricingDiscounts(
        filters: {
            organizationId: IDatabaseObjectId;
            itemId: Types.ObjectId;
            type?: DiscountType;
            target?: ENUM_PROMOTION_TARGET;
            isActive?: boolean;
            search?: Record<string, any>;
            startDate?: Date;
            endDate?: Date;
            facilityId?: IDatabaseObjectId[];
        },
        options: IDatabaseFindAllOptions
    ) {
        const { organizationId, itemId, type, target, isActive, search, startDate, endDate, facilityId } = filters;
        const { paging: { limit, offset } } = options;

        // Convert facilityId to array of ObjectIds if provided
        const facilityIds = facilityId ?
            (Array.isArray(facilityId) ? facilityId.map(id => new Types.ObjectId(id)) : [new Types.ObjectId(facilityId)])
            : [];

        const pipeline: PipelineStage[] = [
            // Match promotion items for the given pricing
            {
                $match: {
                    item: itemId,
                    isActive: true
                }
            },
            // Lookup promotions
            {
                $lookup: {
                    from: 'promotions',
                    localField: 'promotion',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $match: {
                                deletedAt: {
                                    $exists: false
                                },
                                organizationId,
                                ...(type && { type }),
                                ...(target && { target }),
                                ...(isActive !== undefined && { isActive }),
                                ...(search && search),
                                ...(startDate &&
                                {
                                    $or: [
                                        { startDate: { $lte: startDate } },
                                        { startDate: null }
                                    ]
                                }
                                ),
                                ...(endDate &&
                                {
                                    $or: [
                                        { endDate: { $gte: endDate } },
                                        { endDate: null }
                                    ]
                                }
                                ),
                                ...(facilityIds.length && {
                                    $or: [
                                        { $expr: { $gt: [{ $size: { $setIntersection: ["$facilityIds", facilityIds] } }, 0] } },
                                        { facilityIds: { $size: 0 } },
                                        { facilityIds: null }
                                    ]
                                })
                            }
                        }
                    ],
                    as: 'promotionDetails'
                }
            },
            // Unwind promotion details
            {
                $unwind: '$promotionDetails'
            },
            // Project required fields
            {
                $project: {
                    _id: '$promotionDetails._id',
                    name: '$promotionDetails.name',
                    description: '$promotionDetails.description',
                    type: '$promotionDetails.type',
                    value: '$promotionDetails.value',
                    target: '$promotionDetails.target',
                    itemType: '$promotionDetails.itemType',
                    startDate: '$promotionDetails.startDate',
                    endDate: '$promotionDetails.endDate',
                    isActive: '$promotionDetails.isActive',
                    promotionLabel: '$promotionDetails.promotionLabel'
                }
            },
            // Apply sorting
            {
                $facet: {
                    data: [
                        // ...(order ? [{ $sort: order }] : []),
                        { $sort: { name: 1 } },
                        { $skip: offset },
                        { $limit: limit }
                    ],
                    total: [{ $count: 'total' }]
                }
            },
            // Project final result
            {
                $project: {
                    data: 1,
                    total: { $arrayElemAt: ['$total.total', 0] }
                }
            }
        ];
        const results = await this.promotionItemRepository.aggregate(pipeline);

        return {
            data: results[0]?.data || [],
            total: results[0]?.total || 0
        };
    }

    async calculateDiscountedPrice(
        itemPrice: number,
        promotion: {
            type: DiscountType;
            value: number;
        }
    ): Promise<number> {
        let discountedPrice = 0;
        if (promotion.type === DiscountType.PERCENTAGE) {
            discountedPrice = (promotion.value / 100) * itemPrice;
        } else if (promotion.type === DiscountType.FLAT) {
            discountedPrice = itemPrice - promotion.value;
        }
        return discountedPrice;
    }

    async calculateDiscountedValue(
        itemPrice: number,
        promotion: {
            type: DiscountType;
            value: number;
        }
    ): Promise<number> {
        let discountedValue = 0;
        if (promotion.type === DiscountType.PERCENTAGE) {
            discountedValue = Number(((promotion.value / 100) * itemPrice).toFixed(2));
        } else if (promotion.type === DiscountType.FLAT) {
            discountedValue = Number((promotion.value ? promotion.value : 0).toFixed(2));
        } else {
            discountedValue = 0.00;
        }
        return discountedValue;
    }

    async validateItemDiscount(promotion: PromotionDocument, itemValue: number | Number) {
        if (!promotion) {
            throw new BadRequestException("Invalid promotion");
        }
        if (!promotion.isActive || (promotion.endDate && promotion.endDate < new Date())) {
            throw new BadRequestException(`${promotion.name ?? ""} promotion is not active or expired`);
        }
        if (promotion.type === DiscountType.FLAT && (promotion.value < 0 || promotion.value > (itemValue as any))) {
            throw new BadRequestException(`Invalid discount value. ${promotion.name ?? ""} promotion value ${promotion.value} cannot be less than 0 or greater than ${itemValue} for a item`);
        }
        if (promotion.type === DiscountType.PERCENTAGE && (promotion.value < 0 || promotion.value > 100)) {
            throw new BadRequestException(`Invalid discount value. ${promotion.name ?? ""} promotion value ${promotion.value} cannot be less than 0 or greater than 100 for a percentage discount`);
        }
        return promotion;
    }

    /**
     * Validate a promotion code for a cart
     * @param promotionCode The promotion code to validate
     * @param organizationId The organization ID
     * @param isMember Whether the user is a member
     * @returns An object with validation result and promotion if valid
     */
    async validatePromotionCode(promotionCode: string, organizationId: Types.ObjectId, isMember: boolean, facilityId?: string): Promise<{ isValid: boolean; promotion?: PromotionDocument; errorMessage?: string }> {
        try {
            // Find promotion by code
            const promotion = await this.findOne({
                organizationId,
                couponCode: promotionCode,
                isActive: true,
            });

            // Check if promotion exists
            if (!promotion) {
                return { isValid: false, errorMessage: `Promotion code ${promotionCode} is invalid or expired` };
            }

            // Check if promotion is valid for the facility
            if (promotion.facilityIds?.length) {
                const facilityIdObj = new Types.ObjectId(facilityId);
                if (!promotion.facilityIds.some((id: Types.ObjectId) => id.equals(facilityIdObj))) {
                    return { isValid: false, errorMessage: `Promotion code ${promotionCode} is not valid for this facility` };
                }
            }

            // Check if promotion is valid for the user (member-only check)
            if (promotion.target === ENUM_PROMOTION_TARGET.MEMBERS_ONLY && !isMember) {
                return { isValid: false, errorMessage: `Promotion code ${promotionCode} is for members only` };
            }

            // Check promotion dates
            const now = new Date();
            if (
                (promotion.startDate && promotion.startDate > now) ||
                (promotion.endDate && promotion.endDate < now)
            ) {
                return { isValid: false, errorMessage: `Promotion code ${promotionCode} is not valid at this time` };
            }

            // Check time window if present
            if (promotion.timeWindow) {
                const currentTime = new Date().toTimeString().substring(0, 5); // Format: HH:MM
                if (
                    promotion.timeWindow.startTime > currentTime ||
                    promotion.timeWindow.endTime < currentTime
                ) {
                    return { isValid: false, errorMessage: `Promotion code ${promotionCode} is not valid at this time of day` };
                }
            }

            // Promotion is valid
            return { isValid: true, promotion };
        } catch (error) {
            return { isValid: false, errorMessage: `Error validating promotion code: ${error.message}` };
        }
    }

    /**
     * Calculate the best discount for an item based on available promotions
     * @param totalPrice The total price of the item
     * @param quantity The quantity of the item
     * @param itemDiscount The item's own discount (if any)
     * @returns The best discount information
     */
    async calculateBestDiscount(
        totalPrice: number,
        quantity: number,
        itemDiscount?: { type: string; value: number },
    ): Promise<{ discountAmount: number; discountType?: DiscountType; discountValue?: number }> {
        // Initialize with item's own discount if available
        let discountAmount = 0;
        let discountType: DiscountType | undefined;
        let discountValue: number | undefined;

        if (itemDiscount && itemDiscount.type && itemDiscount.value) {
            if (itemDiscount.type === DiscountType.PERCENTAGE) {
                discountAmount = Math.round((itemDiscount.value / 100) * totalPrice);
                discountType = DiscountType.PERCENTAGE;
                discountValue = itemDiscount.value;
            } else if (itemDiscount.type === DiscountType.FLAT) {
                discountAmount = Math.min(itemDiscount.value * quantity, totalPrice);
                discountType = DiscountType.FLAT;
                discountValue = itemDiscount.value;
            }
        }

        return { discountAmount, discountType, discountValue };
    }

    /**
     * Get a promotion by ID (public method)
     * @param promotionId The promotion ID
     * @returns The promotion document or null if not found
     */
    async getPromotionById(promotionId: string): Promise<PromotionDocument | null> {
        try {
            return await this.promotionRepository.findOneById(new Types.ObjectId(promotionId));
        } catch (error) {
            console.error('Error fetching promotion by ID:', error);
            return null;
        }
    }
}
